"""
Enhanced AI Ship - Refactored and improved AI system for Escape Velocity Py
This is the main AI ship class that coordinates all AI subsystems.
"""
import pygame as pg
import random
import math
from game_objects.ai_core import AIShipCore, AI_STATE_ATTACKING, AI_STATE_FLEEING, AI_STATE_DISABLED
from game_objects.ai_states import AIStateManager
from game_objects.ai_sensors import AISensorManager
from game_objects.ai_combat import AICombatManager

class AIShip(AIShipCore):
    """
    Enhanced AI Ship with modular subsystems.
    Inherits basic functionality from AIShipCore and adds advanced AI behavior.
    """

    def __init__(self, game, pos_x, pos_y, faction_id, ship_id="scout"):
        super().__init__(game, pos_x, pos_y, faction_id, ship_id)

        # Initialize AI subsystems
        self.state_manager = AIStateManager(self)
        self.sensor_manager = AISensorManager(self)
        self.combat_manager = AICombatManager(self)

        # Add frame counter reference for timing
        if not hasattr(self.game, 'frame_count'):
            self.game.frame_count = 0

        print(f"Enhanced AI Ship created: {self.ship_type} ({self.faction_id})")
        print(f"  - Weapons: {len(self.weapons)}")
        print(f"  - Fixed weapons: {self.has_fixed_weapons}")
        print(f"  - Turret weapons: {self.has_turret_weapons}")
        print(f"  - Missile weapons: {self.has_missile_weapons}")
        print(f"  - Personality: {self.personality}")

    def update(self, dt=1/60):
        """Main update loop for the AI ship."""
        # Update frame counter
        self.game.frame_count = getattr(self.game, 'frame_count', 0) + 1

        # Update timers
        self.state_timer -= 1
        if self.shield_recharge_timer > 0:
            self.shield_recharge_timer -= 1
        elif self.shields < self.max_shields:
            self.shields = min(self.max_shields, self.shields + 0.1)

        # Power regeneration (if ship has power system)
        if hasattr(self.ship, 'regenerate_power'):
            self.ship.regenerate_power(dt)

        # Update AI subsystems
        self.sensor_manager.update(dt)
        self.state_manager.update(dt)

        # Update weapons
        for weapon in self.weapons:
            weapon.update(dt)

        # Update projectiles
        for projectile in list(self.projectiles):
            if projectile.update(dt):
                projectile.kill()

        # Apply movement and rotation
        self.apply_movement()
        self.rotate_image()

    def take_damage(self, amount, attacker_faction_id="unknown"):
        """
        Handle taking damage and determine response.

        Args:
            amount: Damage amount
            attacker_faction_id: Faction ID of the attacker
        """
        # Apply damage to shields first, then health
        if self.shields > 0:
            self.shields -= amount
            if self.shields < 0:
                self.health += self.shields  # Overflow damage to health
                self.shields = 0
        else:
            self.health -= amount

        self.shield_recharge_timer = self.shield_recharge_delay

        # Handle death
        if self.health <= 0:
            self.kill()
            print(f"{self.ship_type} ({self.faction_id}) destroyed!")
            self.create_explosion()
            self.drop_loot()
            return

        # Handle disable state - ship becomes disabled when shields are 0 and armor is at 15% or less
        disable_threshold = self.max_health * 0.15  # 15% armor threshold
        if self.shields <= 0 and self.health <= disable_threshold:
            if self.ai_state != AI_STATE_DISABLED:
                self.ai_state = AI_STATE_DISABLED
                print(f"DEBUG: {self.ship_type} ({self.faction_id}) disabled (shields: {self.shields:.1f}, armor: {self.health:.1f}/{self.max_health}).")
                self.game.set_status_message(f"{self.ship_type} disabled! Press B to board.", (255, 255, 0))
            return

        # Combat response logic
        if self.ai_state != AI_STATE_DISABLED:
            attacker_entity = self._find_attacker(attacker_faction_id)

            if attacker_entity:
                # Check if we should flee
                if self.sensor_manager.should_flee():
                    self.target_entity = attacker_entity
                    self.ai_state = AI_STATE_FLEEING
                    print(f"{self.faction_id} ship fleeing from {attacker_faction_id}!")
                    self.game.set_status_message(f"{self.faction_id} ship fleeing!", (255, 255, 0))
                elif self.ai_state != AI_STATE_ATTACKING:
                    # Fight back
                    self.target_entity = attacker_entity
                    self.ai_state = AI_STATE_ATTACKING
                    print(f"{self.faction_id} ship fighting back against {attacker_faction_id}!")
                    self.game.set_status_message(f"{self.faction_id} ship attacking!", (255, 100, 100))

    def _find_attacker(self, attacker_faction_id):
        """
        Find the entity that attacked us.

        Args:
            attacker_faction_id: Faction ID of the attacker

        Returns:
            Entity that attacked us, or None if not found
        """
        if attacker_faction_id == "player":
            return self.game.player
        else:
            # Find AI ship with matching faction
            for ship in self.game.ai_ships:
                if ship.faction_id == attacker_faction_id:
                    return ship
        return None

    def create_explosion(self):
        """Create an explosion effect when the ship is destroyed."""
        print(f"Explosion at {self.pos}")
        # TODO: Add visual explosion effect

    def drop_loot(self):
        """Drop loot when the ship is destroyed."""
        # Calculate loot value based on ship size
        size_multipliers = {"small": 1, "medium": 2, "large": 3, "capital": 5}
        base_loot_value = 100 * size_multipliers.get(self.ship.size, 1)
        loot_value = int(base_loot_value * random.uniform(0.5, 1.5))

        if hasattr(self.game, 'player') and self.game.player:
            self.game.player.credits += loot_value
            self.game.set_status_message(f"Collected {loot_value} credits from destroyed ship", (0, 255, 0))

        print(f"Dropped {loot_value} credits")

    def get_combat_effectiveness(self):
        """
        Calculate the combat effectiveness of this ship.

        Returns:
            float: Combat effectiveness score
        """
        if not self.weapons:
            return 0

        total_dps = sum(weapon.damage * weapon.fire_rate for weapon in self.weapons)
        health_factor = (self.health + self.shields) / (self.max_health + self.max_shields)

        return total_dps * health_factor

    def get_weapon_loadout_description(self):
        """
        Get a description of the ship's weapon loadout.

        Returns:
            str: Description of weapons
        """
        if not self.weapons:
            return "Unarmed"

        weapon_types = []
        if self.has_fixed_weapons:
            weapon_types.append("Fixed")
        if self.has_turret_weapons:
            weapon_types.append("Turret")
        if self.has_missile_weapons:
            weapon_types.append("Missile")

        return f"{len(self.weapons)} weapons ({', '.join(weapon_types)})"

    def debug_info(self):
        """
        Get debug information about the AI ship.

        Returns:
            dict: Debug information
        """
        return {
            'ship_type': self.ship_type,
            'faction': self.faction_id,
            'ai_state': self.ai_state,
            'combat_state': getattr(self, 'combat_state', 'N/A'),
            'personality': self.personality,
            'health': f"{self.health:.1f}/{self.max_health}",
            'shields': f"{self.shields:.1f}/{self.max_shields}",
            'target': self.target_entity.ship_type if self.target_entity and hasattr(self.target_entity, 'ship_type') else 'None',
            'weapons': len(self.weapons),
            'weapon_loadout': self.get_weapon_loadout_description(),
            'detected_targets': len(self.sensor_manager.detected_targets),
            'position': f"({self.pos.x:.0f}, {self.pos.y:.0f})"
        }

# Export the constants and classes that other modules need
__all__ = [
    'AIShip',
    'AI_STATE_IDLE', 'AI_STATE_PATROLLING', 'AI_STATE_ATTACKING',
    'AI_STATE_FLEEING', 'AI_STATE_TRADING', 'AI_STATE_DISABLED'
]
