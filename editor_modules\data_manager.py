"""
Data Manager for the Enhanced Content Editor
Handles loading, saving, and managing game data
"""

import json
import os
import sys
from pathlib import Path

class DataManager:
    """Manages all game data for the editor."""

    def __init__(self):
        self.outfits_registry = {}
        self.ships_registry = {}
        self.standardized_ships_registry = {}

        # Determine the game root directory (where JSON files should be)
        self.game_root = Path(__file__).parent.parent  # Go up from editor_modules to EscapeVelocityPy
        print(f"Editor Data Manager: Game root directory = {self.game_root}")

        # Initialize game data
        self.load_game_data()

    def load_game_data(self):
        """Load game data from the game's source files."""
        try:
            # Add the game's src directory to the path
            game_src_path = Path(__file__).parent.parent / "src"
            sys.path.insert(0, str(game_src_path))

            # Import game systems
            from game_objects.standardized_outfits import OUTFITS_REGISTRY
            from game_objects.ships import SHIPS
            from game_objects.standardized_ships import STANDARDIZED_SHIPS_REGISTRY

            # Import example outfits module to trigger loading (this loads from JSON or creates examples)
            import game_objects.example_outfits  # This triggers the outfit loading process

            self.outfits_registry = OUTFITS_REGISTRY
            self.ships_registry = SHIPS
            self.standardized_ships_registry = STANDARDIZED_SHIPS_REGISTRY

            print(f"Successfully loaded game data:")
            print(f"  - {len(self.outfits_registry)} outfits")
            print(f"  - {len(self.ships_registry)} ships")
            print(f"  - {len(self.standardized_ships_registry)} standardized ships")

            # If we have outfits but no JSON file, export them for next time
            if len(self.outfits_registry) > 0:
                outfits_json = self.game_root / "outfits_data.json"
                if not outfits_json.exists():
                    print("No outfits JSON found but outfits are loaded. Creating initial JSON file...")
                    self.save_outfits_data()

            # Load any saved data (this will update outfits with saved changes)
            self.load_saved_data()

        except ImportError as e:
            print(f"Error importing game systems: {e}")
            print("Make sure the game files are in the correct location")

    def load_saved_data(self):
        """Load previously saved outfit and ship data."""
        self.load_saved_outfits()
        self.load_saved_ships()

    def load_saved_outfits(self):
        """Load previously saved outfit data if available."""
        save_file = self.game_root / "outfits_data.json"
        print(f"Editor: Looking for outfits data at {save_file}")
        if save_file.exists():
            try:
                with open(save_file, 'r') as f:
                    saved_data = json.load(f)

                # Update existing outfits with saved data
                for outfit_id, outfit_data in saved_data.items():
                    if outfit_id in self.outfits_registry:
                        outfit = self.outfits_registry[outfit_id]
                        self._update_outfit_from_data(outfit, outfit_data)

                print(f"Loaded saved outfit data from {save_file}")
            except Exception as e:
                print(f"Failed to load saved outfit data: {e}")

    def load_saved_ships(self):
        """Load previously saved ship data if available."""
        save_file = self.game_root / "ships_data.json"
        print(f"Editor: Looking for ships data at {save_file}")
        if save_file.exists():
            try:
                with open(save_file, 'r') as f:
                    saved_data = json.load(f)

                # Update existing ships with saved data
                for ship_id, ship_data in saved_data.items():
                    if ship_id in self.ships_registry:
                        ship = self.ships_registry[ship_id]
                        self._update_ship_from_data(ship, ship_data)

                print(f"Loaded saved ship data from {save_file}")
            except Exception as e:
                print(f"Failed to load saved ship data: {e}")

    def _update_outfit_from_data(self, outfit, outfit_data):
        """Update an outfit object with data from JSON."""
        # Update basic properties
        for prop in ['name', 'cost', 'space_required', 'min_tech_level', 'outfitter_icon', 'outfitter_image', 'description']:
            if prop in outfit_data:
                # Always set outfitter_image even if the attribute doesn't exist yet
                if prop == 'outfitter_image' or hasattr(outfit, prop):
                    setattr(outfit, prop, outfit_data[prop])

        # Update category-specific properties based on outfit category
        if hasattr(outfit, 'category'):
            if outfit.category == "weapons":
                for prop in ['mount_type', 'damage', 'fire_rate', 'range', 'energy_usage', 'uses_ammo', 'ammo_type', 'max_ammo', 'fire_sound']:
                    if prop in outfit_data:
                        # Always set fire_sound even if the attribute doesn't exist yet
                        if prop == 'fire_sound' or hasattr(outfit, prop):
                            setattr(outfit, prop, outfit_data[prop])
            elif outfit.category == "ammunition":
                for prop in ['ammo_type', 'quantity', 'damage', 'projectile_speed', 'projectile_behavior', 'tracking_strength', 'explosion_radius']:
                    if prop in outfit_data and hasattr(outfit, prop):
                        setattr(outfit, prop, outfit_data[prop])
            elif outfit.category == "defense":
                for prop in ['shield_boost', 'armor_boost', 'shield_recharge_boost', 'damage_reduction']:
                    if prop in outfit_data and hasattr(outfit, prop):
                        setattr(outfit, prop, outfit_data[prop])

    def _update_ship_from_data(self, ship, ship_data):
        """Update a ship object with data from JSON."""
        # Update all ship properties
        for prop, value in ship_data.items():
            if hasattr(ship, prop):
                setattr(ship, prop, value)

    def save_outfits_data(self, filename="outfits_data.json"):
        """Save all outfit data to JSON file."""
        try:
            # Always save to the game root directory
            save_path = self.game_root / filename
            print(f"Editor: Saving outfits data to {save_path}")

            outfits_data = {}
            for outfit_id, outfit in self.outfits_registry.items():
                outfits_data[outfit_id] = self._outfit_to_dict(outfit)

            with open(save_path, 'w') as f:
                json.dump(outfits_data, f, indent=2)

            print(f"Saved {len(outfits_data)} outfits to {save_path}")
            return True
        except Exception as e:
            print(f"Failed to save outfits: {e}")
            return False

    def save_ships_data(self, filename="ships_data.json"):
        """Save all ship data to JSON file."""
        try:
            # Always save to the game root directory
            save_path = self.game_root / filename
            print(f"Editor: Saving ships data to {save_path}")

            ships_data = {}
            for ship_id, ship in self.ships_registry.items():
                ships_data[ship_id] = self._ship_to_dict(ship)

            with open(save_path, 'w') as f:
                json.dump(ships_data, f, indent=2)

            print(f"Saved {len(ships_data)} ships to {save_path}")
            return True
        except Exception as e:
            print(f"Failed to save ships: {e}")
            return False

    def _outfit_to_dict(self, outfit):
        """Convert an outfit object to a dictionary for JSON export."""
        outfit_data = {
            'id': getattr(outfit, 'id', outfit.name.lower().replace(' ', '_')),
            'name': outfit.name,
            'category': getattr(outfit, 'category', 'unknown'),
            'subcategory': getattr(outfit, 'subcategory', ''),
            'cost': getattr(outfit, 'cost', 1000),
            'space_required': getattr(outfit, 'space_required', 1),
            'min_tech_level': getattr(outfit, 'min_tech_level', 1),
            'outfitter_icon': getattr(outfit, 'outfitter_icon', ''),
            'outfitter_image': getattr(outfit, 'outfitter_image', ''),
            'description': getattr(outfit, 'description', ''),
        }

        # Add category-specific properties
        if hasattr(outfit, 'category'):
            if outfit.category == "weapons":
                outfit_data.update({
                    'mount_type': getattr(outfit, 'mount_type', 'fixed'),
                    'damage': getattr(outfit, 'damage', 10),
                    'fire_rate': getattr(outfit, 'fire_rate', 1.0),
                    'range': getattr(outfit, 'range', 300),
                    'energy_usage': getattr(outfit, 'energy_usage', 5),
                    'uses_ammo': getattr(outfit, 'uses_ammo', False),
                    'ammo_type': getattr(outfit, 'ammo_type', ''),
                    'max_ammo': getattr(outfit, 'max_ammo', 0),
                    'fire_sound': getattr(outfit, 'fire_sound', '')
                })
            elif outfit.category == "ammunition":
                outfit_data.update({
                    'ammo_type': getattr(outfit, 'ammo_type', ''),
                    'quantity': getattr(outfit, 'quantity', 10),
                    'damage': getattr(outfit, 'damage', 50),
                    'projectile_speed': getattr(outfit, 'projectile_speed', 8),
                    'projectile_behavior': getattr(outfit, 'projectile_behavior', 'dumbfire'),
                    'tracking_strength': getattr(outfit, 'tracking_strength', 0.0),
                    'explosion_radius': getattr(outfit, 'explosion_radius', 20)
                })
            elif outfit.category == "defense":
                outfit_data.update({
                    'shield_boost': getattr(outfit, 'shield_boost', 0),
                    'armor_boost': getattr(outfit, 'armor_boost', 0),
                    'shield_recharge_boost': getattr(outfit, 'shield_recharge_boost', 0.0),
                    'damage_reduction': getattr(outfit, 'damage_reduction', 0.0)
                })

        return outfit_data

    def _ship_to_dict(self, ship):
        """Convert a ship object to a dictionary for JSON export."""
        return {
            'id': getattr(ship, 'id', ship.name.lower().replace(' ', '_')),
            'name': ship.name,
            'ship_class': getattr(ship, 'ship_class', 'fighter'),
            'size': getattr(ship, 'size', 'small'),
            'cost': getattr(ship, 'cost', 10000),
            'outfit_space': getattr(ship, 'outfit_space', 20),
            'cargo_space': getattr(ship, 'cargo_space', 10),
            'mass': getattr(ship, 'mass', 1.0),
            'min_tech_level': getattr(ship, 'min_tech_level', 1),
            'max_speed': getattr(ship, 'max_speed', 3.0),
            'acceleration': getattr(ship, 'acceleration', 0.5),
            'turn_rate': getattr(ship, 'turn_rate', 1.0),
            'shields': getattr(ship, 'shields', 50),
            'armor': getattr(ship, 'armor', 30),
            'shield_recharge_rate': getattr(ship, 'shield_recharge_rate', 1.0),
            'description': getattr(ship, 'description', ''),
            'shipyard_display_image': getattr(ship, 'shipyard_display_image', ''),
        }

    def get_outfits_by_category(self, category):
        """Get all outfits of a specific category."""
        return {
            outfit_id: outfit for outfit_id, outfit in self.outfits_registry.items()
            if (hasattr(outfit, 'category') and outfit.category == category) or
               (hasattr(outfit, '__class__') and category.title() in str(outfit.__class__))
        }

    def get_all_ships(self):
        """Get all ships."""
        return self.ships_registry if self.ships_registry else self.standardized_ships_registry

    def auto_save(self):
        """Auto-save all data."""
        self.save_outfits_data()
        self.save_ships_data()
